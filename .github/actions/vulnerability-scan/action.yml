name: Vulnerability Scan
description: Scans a specified Dockerfile or Image URI for vulnerabilities using Grype
author: "unlockre"

inputs:
  github_token:
    description: 'GitHub token for API access'
    required: true
    default: ${{ github.token }}
  severity_cutoff:
    description: 'Minimum severity level to report (low, medium, high, critical)'
    required: false
    default: 'high'
  only_fixed:
    description: 'Only report vulnerabilities with fixes'
    required: false
    default: 'true'
  image_uri:
    description: 'Image URI to scan'
    required: false
  dockerfile:
    description: 'Path to the Dockerfile to scan'
    required: false

outputs:
  has_vulnerabilities:
    description: 'Whether vulnerabilities meeting the cutoff were found after filtering'
    value: ${{ steps.check-vulnerabilities.outputs.has_vulnerabilities }}

runs:
  using: "composite"
  steps:
    - name: Fail if no image source specified
      if: inputs.dockerfile == '' && inputs.image_uri == ''
      shell: bash
      run: |
        echo "Error: Neither dockerfile nor image_uri was specified. At least one must be provided."
        exit 1

    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Determine Scan Category and Image Source
      id: scan-source-info
      shell: bash
      run: |
        DOCKERFILE_INPUT="${{ inputs.dockerfile }}"
        IMAGE_URI_INPUT="${{ inputs.image_uri }}"

        # Set category based on which input is provided
        if [ -n "$DOCKERFILE_INPUT" ]; then
          echo "Processing Dockerfile: $DOCKERFILE_INPUT"
          if [ ! -f "$DOCKERFILE_INPUT" ]; then
            echo "Error: Dockerfile $DOCKERFILE_INPUT not found"
            exit 1
          fi
          # Sanitize the path for use in tags/categories
          SANITIZED_PATH=$(echo "$DOCKERFILE_INPUT" | sed -E 's/[^a-zA-Z0-9]+/-/g' | tr '[:upper:]' '[:lower:]')
          # Remove leading/trailing hyphens
          SANITIZED_PATH=$(echo "$SANITIZED_PATH" | sed -E 's/^-+|-+$//g')

          IMAGE_TAG="localbuild/testimage-${SANITIZED_PATH}:latest"
          CATEGORY="grype-${SANITIZED_PATH}"
          SCAN_IMAGE_SOURCE="$IMAGE_TAG"

          echo "image_tag=$IMAGE_TAG" >> $GITHUB_OUTPUT
          echo "category=$CATEGORY" >> $GITHUB_OUTPUT
          echo "scan_image_source=$SCAN_IMAGE_SOURCE" >> $GITHUB_OUTPUT

        elif [ -n "$IMAGE_URI_INPUT" ]; then
          echo "Processing Image URI: $IMAGE_URI_INPUT"
          # Replace problematic characters in the URI for the category name
          CATEGORY=$(echo "$IMAGE_URI_INPUT" | sed -E 's/[^a-zA-Z0-9:\/.-]+/-/g' | sed -E 's/[\/.:-]+/_/g' | tr '[:upper:]' '[:lower:]')
          CATEGORY="grype-image-${CATEGORY}"
          SCAN_IMAGE_SOURCE="$IMAGE_URI_INPUT" # Grype will scan the provided image URI

          echo "category=$CATEGORY" >> $GITHUB_OUTPUT
          echo "scan_image_source=$SCAN_IMAGE_SOURCE" >> $GITHUB_OUTPUT

        else
          # This case should ideally not be reached due to the initial check,
          # but as a fallback, output empty values to avoid errors in downstream steps
          echo "Warning: Neither dockerfile nor image_uri provided. This should have been caught earlier."
          echo "image_tag=" >> $GITHUB_OUTPUT
          echo "category=unknown-scan" >> $GITHUB_OUTPUT
          echo "scan_image_source=" >> $GITHUB_OUTPUT
        fi
      env:
        GITHUB_TOKEN: ${{ inputs.github_token }}

    - name: Build Docker Image
      uses: docker/build-push-action@v6
      if: inputs.dockerfile != '' # Only run if dockerfile was the input
      with:
        context: .
        file: ${{ inputs.dockerfile }}
        tags: ${{ steps.scan-source-info.outputs.image_tag }}
        load: true

    - name: Scan Image with Grype
      id: grype-scan
      uses: anchore/scan-action@v6
      with:
        image: ${{ steps.scan-source-info.outputs.scan_image_source }}
        output-format: sarif
        output-file: scan-results-${{ steps.scan-source-info.outputs.category }}.sarif
        severity-cutoff: negligible # Grype outputs more, jq filters precisely
        only-fixed: ${{ inputs.only_fixed }}
        fail-build: false # We handle failure based on our filtered results

    - name: Debug Initial SARIF Content
      shell: bash
      run: |
        sarif_file="scan-results-${{ steps.scan-source-info.outputs.category }}.sarif"
        echo "=== Initial SARIF Content ==="
        if [ -f "$sarif_file" ]; then
          echo "File exists with size: $(stat -f%z "$sarif_file") bytes"
          echo "First 1000 characters:"
          head -c 1000 "$sarif_file"
          echo -e "\n=== End Initial Content ==="
        else
          echo "Error: Initial SARIF file not found!"
        fi

    - name: Filter SARIF Report
      shell: bash
      run: |
        # Use the category from the previous step
        sarif_file="scan-results-${{ steps.scan-source-info.outputs.category }}.sarif"
        temp_file="temp-${sarif_file}"
        input_severity_cutoff="${{ inputs.severity_cutoff }}"

        echo "Listing directory before processing..."
        ls -l

        if [ ! -f "$sarif_file" ]; then
          echo "Error: SARIF file $sarif_file not found before filtering."
          # Create a minimally valid empty SARIF to prevent downstream errors
          echo '{ "version": "2.1.0", "$schema": "https://docs.oasis-open.org/sarif/sarif/v2.1.0/errata01/os/schemas/sarif-schema-2.1.0.json", "runs": [ { "tool": { "driver": { "name": "grype-filter-placeholder" } }, "results": [] } ] }' > "$sarif_file"
          echo "Created empty SARIF file and skipping filtering."
          exit 0
        fi

        # Validate SARIF file structure before filtering
        if ! jq empty "$sarif_file" 2>/dev/null; then
          echo "Error: SARIF file is not valid JSON"
          exit 1
        fi

        # Check if the file has the expected structure
        if ! jq -e '.runs[0].results' "$sarif_file" >/dev/null 2>&1; then
          echo "Warning: SARIF file does not have the expected structure"
          echo "File content:"
          cat "$sarif_file"
          exit 1
        fi

        echo "Debug: Input severity_cutoff for filtering: $input_severity_cutoff"
        echo "Debug: Extracting rule severity-related fields from SARIF file $sarif_file (if rules exist)"
        jq '([.runs[] | .tool.driver.rules // []] | flatten)[] | {id: .id, shortDescription: (.shortDescription.text // ""), securitySeverity: (.properties."security-severity" // "N/A")}' "$sarif_file" > "debug_rule_severity_values-${{ steps.scan-source-info.outputs.category }}.txt" || echo "Debug info extraction failed or no rules found."

        echo "Total results before filtering in $sarif_file:"
        jq '(.runs[0].results // []) | length' "$sarif_file"

        # Perform the filtering with better error handling
        if ! jq --arg current_severity_cutoff "'$input_severity_cutoff'" '
        # Define severity levels mapping
        def severity_levels: {
          "negligible": 1, "low": 2, "medium": 3, "high": 4, "critical": 5
        };

        # Map input severity cutoff to numeric value using the passed argument
        def cutoff: severity_levels[$current_severity_cutoff | ascii_downcase] // (if $current_severity_cutoff == "all" then 0 else 4 end);

        # Function to map CVSS score to severity string (jq 1.5 compatible)
        def get_severity_string(rule_properties):
          if rule_properties == null then "unknown"
          elif rule_properties | has("security-severity") then
            (rule_properties."security-severity") as $sec_sev_value |
            if $sec_sev_value | type == "string" then
              (try ($sec_sev_value | tonumber) catch null) as $cvss_num_or_null |
              if $cvss_num_or_null == null then "unknown"
              else
                if $cvss_num_or_null >= 9.0 then "critical"
                elif $cvss_num_or_null >= 7.0 then "high"
                elif $cvss_num_or_null >= 4.0 then "medium"
                elif $cvss_num_or_null >= 0.1 then "low"
                else "negligible"
                end
              end
            else "unknown"
            end
          else "unknown"
          end;

        # Process runs
        .runs = (
          (.runs // []) | map(
            if . == null then .
            else
              (.tool.driver.rules // []) as $rules_list |
              ( [$rules_list[] | if . != null and (.id != null) then {(.id): .} else empty end] | add // {} ) as $rules_by_id |
              .results = (
                (.results // []) | map(
                  . as $result_item |
                  ($result_item.ruleId // "") as $current_rule_id |
                  ($rules_by_id[$current_rule_id]) as $rule |
                  (get_severity_string(if $rule == null then null else $rule.properties end)) as $result_severity_string |
                  if (severity_levels[$result_severity_string] // 0) >= cutoff then
                    $result_item
                  else
                    empty
                  end
                )
              )
            end
          )
        )
        ' "$sarif_file" > "$temp_file"; then
          echo "Error: jq filtering failed. Check SARIF format and jq script."
          echo "Original SARIF content:"
          cat "$sarif_file"
          rm -f "$temp_file"
          exit 1
        fi

        # Verify temp file exists and has content
        if [ ! -f "$temp_file" ]; then
          echo "Error: Temp file $temp_file was not created by jq."
          exit 1
        fi

        # Validate the filtered output is still valid JSON
        if ! jq empty "$temp_file" 2>/dev/null; then
          echo "Error: Filtered output is not valid JSON"
          echo "Filtered content:"
          cat "$temp_file"
          rm -f "$temp_file"
          exit 1
        fi

        # Get the number of results after filtering
        if ! filtered_count=$(jq '(.runs[0].results // []) | length' "$temp_file" 2>/dev/null); then
          echo "Error: Failed to extract filtered count from temp file"
          echo "Temp file content:"
          cat "$temp_file"
          filtered_count="unknown"
        fi
        echo "Total results after filtering: $filtered_count"

        # Move the temp file to the final location
        mv "$temp_file" "$sarif_file"
        
        # Verify the final file exists and has content
        if [ ! -f "$sarif_file" ]; then
          echo "Error: Final SARIF file $sarif_file was not created."
          exit 1
        fi

        echo "SARIF filtering completed successfully."
      env:
        GITHUB_TOKEN: ${{ inputs.github_token }}

    - name: Debug Filtered SARIF Content
      shell: bash
      run: |
        sarif_file="scan-results-${{ steps.scan-source-info.outputs.category }}.sarif"
        echo "=== Filtered SARIF Content ==="
        if [ -f "$sarif_file" ]; then
          echo "File exists with size: $(stat -f%z "$sarif_file") bytes"
          echo "First 1000 characters:"
          head -c 1000 "$sarif_file"
          echo -e "\n=== End Filtered Content ==="
        else
          echo "Error: Filtered SARIF file not found!"
        fi

    - name: Check Filtered Vulnerabilities
      id: check-vulnerabilities
      shell: bash
      run: |
        # Use the category from the scan-source-info step
        sarif_file="scan-results-${{ steps.scan-source-info.outputs.category }}.sarif"
        echo "Final check on SARIF file: $sarif_file"
        
        if [ ! -f "$sarif_file" ]; then
          echo "Error: Filtered SARIF file $sarif_file not found!"
          echo "has_vulnerabilities=false" >> $GITHUB_OUTPUT
          exit 0
        fi

        # Get vulnerability count with proper error handling
        vulnerability_count=$(jq -r '(.runs[0]?.results? // []) | length' "$sarif_file" 2>/dev/null)
        
        # Validate that vulnerability_count is a number
        if ! [[ "$vulnerability_count" =~ ^[0-9]+$ ]]; then
          echo "Warning: Could not determine valid vulnerability count from SARIF file"
          echo "has_vulnerabilities=false" >> $GITHUB_OUTPUT
          exit 0
        fi

        echo "Found $vulnerability_count vulnerabilities after filtering meeting criteria."
        
        # Use numeric comparison with proper validation
        if [ "$vulnerability_count" -gt 0 ]; then
          echo "has_vulnerabilities=true" >> $GITHUB_OUTPUT
        else
          echo "has_vulnerabilities=false" >> $GITHUB_OUTPUT
        fi

    - name: Upload SARIF Report
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: scan-results-${{ steps.scan-source-info.outputs.category }}.sarif
        category: ${{ steps.scan-source-info.outputs.category }}

    - name: Comment on PR
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        github-token: ${{ inputs.github_token }}
        script: |
          const hasVulnerabilities = '${{ steps.check-vulnerabilities.outputs.has_vulnerabilities }}' === 'true';
          const dockerfile = '${{ inputs.dockerfile }}'; // Still useful to mention which input was used
          const imageUri = '${{ inputs.image_uri }}';
          const severityCutoff = '${{ inputs.severity_cutoff }}';
          // Get the category output into a JavaScript variable
          const sarifCategory = '${{ steps.scan-source-info.outputs.category }}';

          // Determine the comment signature based on the input used
          const commentSignature = dockerfile ?
            `Grype Security scan for \`${dockerfile}\`` :
            `Grype Security scan for image \`${imageUri}\``;

          // Fetch existing comments on the PR
          const { data: existingComments } = await github.rest.issues.listComments({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: context.issue.number,
          });

          // Find and delete previous comments from this action for the same signature
          for (const comment of existingComments) {
            if (comment.user.login === 'github-actions[bot]' && comment.body.includes(commentSignature)) {
              await github.rest.issues.deleteComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: comment.id
              });
            }
          }

          // Construct the URL. The 'tool' parameter should match the 'category' used in upload-sarif.
          // URI-encode the category in case it contains special characters.
          // Note: The query can be complex. A simpler approach might link just to the security tab.
          // For linking directly to the scan results filtered by category/tool, the URL structure
          // is typically https://github.com/{owner}/{repo}/security/code-scanning?query=tool:{category_name}
          // However, filtering by PR number *and* tool in the URL can be tricky depending on UI changes.
          // Linking to the filtered PR view is generally safer.
          const securityTabUrl = `https://github.com/${context.repo.owner}/${context.repo.repo}/security/code-scanning?query=pr%3A${context.issue.number}+is%3Aopen+tool%3A${encodeURIComponent(sarifCategory)}`;

          if (hasVulnerabilities) {
            await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `⚠️ ${commentSignature} has detected '${severityCutoff}' or higher severity vulnerabilities. Please check the [Security tab](${securityTabUrl}) for detailed information.`
            });
          } else {
            await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `✅ ${commentSignature} completed. No vulnerabilities of severity '${severityCutoff}' or higher detected. View the [Security tab](${securityTabUrl}) for details.`
            });
          }

    - name: Fail if vulnerabilities exist
      if: steps.check-vulnerabilities.outputs.has_vulnerabilities == 'true'
      shell: bash
      run: |
        echo "Vulnerabilities of severity ${{ inputs.severity_cutoff }} or higher found in scan category ${{ steps.scan-source-info.outputs.category }}! Check the PR comment and Security tab for details."
