name: Vulnerability Scan

on:
  pull_request:
    branches: [ "master", "main" ]
    types: [ "opened", "reopened", "synchronize", "ready_for_review" ]

permissions:
  security-events: write
  contents: read
  pull-requests: write
  actions: read
  checks: read

jobs:
  vulnerability-scan:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        image_uri: [
          "681574592108.dkr.ecr.us-east-1.amazonaws.com/sre-wolfi-node",
          "681574592108.dkr.ecr.us-east-1.amazonaws.com/sre-wolfi-python"
        ]
      fail-fast: false
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run vulnerability scan
        id: vulnerability-scan
        uses: ./.github/actions/vulnerability-scan
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          severity_cutoff: low
          only_fixed: true
          dockerfile: ${{ matrix.dockerfile }}
          image_uri: ${{ matrix.image_uri }}
